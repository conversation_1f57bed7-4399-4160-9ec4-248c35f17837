{"name": "imapviewer", "productName": "imapviewer", "version": "1.0.0", "description": "My Electron application description", "main": ".vite/build/main.js", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "make:debug": "cross-env DEBUG=electron-forge:*,electron-packager,appx,make-squirrel electron-forge make --verbose", "publish": "electron-forge publish", "lint": "eslint --ext .ts,.tsx ."}, "keywords": [], "author": "Comp", "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.8.1", "@electron-forge/maker-rpm": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.1", "@electron-forge/plugin-fuses": "^7.8.1", "@electron-forge/plugin-vite": "^7.8.1", "@electron/fuses": "^1.8.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.10", "@types/dompurify": "^3.0.5", "@types/fs-extra": "^11.0.4", "@types/imapflow": "^1.0.22", "@types/mailparser": "^3.4.6", "@types/node": "^24.0.3", "@types/react": "^18.2.73", "@types/react-dom": "^18.2.22", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "electron": "^31.0.1", "electron-vite": "^3.1.0", "eslint": "^9.8.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "fs-extra": "^11.3.0", "pino-pretty": "^13.1.1", "postcss": "^8.4.31", "tailwindcss": "^4.0.0-alpha", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite": "^6.3.5"}, "dependencies": {"@azure/msal-node": "^3.6.4", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@vitejs/plugin-react": "^4.5.2", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dompurify": "^3.2.6", "electron-squirrel-startup": "^1.0.1", "framer-motion": "^12.23.6", "https-proxy-agent": "^7.0.6", "imapflow": "^1.0.188", "keytar": "^7.9.0", "lucide-react": "^0.516.0", "mailparser": "^3.7.3", "next-themes": "^0.4.6", "pino": "^9.7.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.58.1", "react-resizable-panels": "^3.0.3", "socks": "^2.8.5", "socks-proxy-agent": "^8.0.5", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "zod": "^3.25.67", "zustand": "^5.0.5"}, "overrides": {"glob": "^10.4.5", "rimraf": "^6.0.1", "eslint": "^9.8.0", "@npmcli/move-file": "^3.0.0", "xterm": "npm:@xterm/xterm@^5.5.0", "xterm-addon-fit": "npm:@xterm/addon-fit@^0.10.0", "xterm-addon-search": "npm:@xterm/addon-search@^0.15.0"}}