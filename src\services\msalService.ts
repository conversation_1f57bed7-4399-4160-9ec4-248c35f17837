import axios, { AxiosError } from 'axios';
import { URLSearchParams } from 'url';
import { getLogger } from './logger';

export interface TokenResponse {
  access_token: string;
  refresh_token?: string;
  expires_in: number;
  scope: string;
  token_type: string;
}

export interface MsalError {
  error: string;
  error_description: string;
  error_codes?: number[];
  timestamp?: string;
  trace_id?: string;
  correlation_id?: string;
}

export class MsalService {
  private static readonly TOKEN_URL = "https://login.microsoftonline.com/common/oauth2/v2.0/token";
  private static readonly RETRY_ATTEMPTS = 3;
  private static readonly RETRY_DELAY = 1000; // 1 second

  // Microsoft OAuth2 scopes in order of preference
  private static readonly SCOPE_FALLBACKS = [
    'https://outlook.office365.com/IMAP.AccessAsUser.All offline_access',
    'https://outlook.office365.com/.default',
    'offline_access'
  ];





  /**
   * Validates if refresh token looks like a Microsoft refresh token
   */
  private static validateMicrosoftRefreshToken(token: string): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    // Microsoft refresh tokens are typically 400+ characters
    if (token.length < 300) {
      issues.push(`Token too short: ${token.length} chars (expected 300+)`);
    }

    // Should start with specific patterns
    if (!token.startsWith('M.') && !token.startsWith('0.') && !token.startsWith('1.')) {
      issues.push(`Unexpected token prefix: ${token.substring(0, 10)}`);
    }

    // Should contain mostly alphanumeric and specific special chars
    const validChars = /^[A-Za-z0-9._!*-]+$/;
    if (!validChars.test(token)) {
      issues.push('Contains invalid characters for Microsoft token');
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * Gets access token using refresh token with improved error handling and retry logic
   */
  static async getAccessToken(
    clientId: string,
    refreshToken: string,
    proxy?: string,
    scope?: string
  ): Promise<TokenResponse> {
    const logger = getLogger();

    // Validate refresh token format
    const tokenValidation = this.validateMicrosoftRefreshToken(refreshToken);

    // Determine scope to use (provided scope or first fallback)
    const scopesToTry = scope ? [scope, ...this.SCOPE_FALLBACKS] : this.SCOPE_FALLBACKS;

    // Basic logging for token request
    logger.info(`[MsalService] Starting token request for client: ${clientId}`);
    logger.debug(`Refresh token length: ${refreshToken.length} chars`);
    logger.debug(`Will try ${scopesToTry.length} scope variants: ${scopesToTry.join(', ')}`);

    if (!tokenValidation.isValid) {
      logger.warn(`Invalid Microsoft refresh token format: ${tokenValidation.issues.join(', ')}`);
    }

    let lastError: Error | null = null;

    // Try each scope variant
    for (let scopeIndex = 0; scopeIndex < scopesToTry.length; scopeIndex++) {
      const currentScope = scopesToTry[scopeIndex];
      logger.info(`[MsalService] Trying scope variant ${scopeIndex + 1}/${scopesToTry.length}: ${currentScope}`);

      // Use URLSearchParams to ensure proper application/x-www-form-urlencoded format
      const params = new URLSearchParams();
      params.append('client_id', clientId);
      params.append('refresh_token', refreshToken);
      params.append('grant_type', 'refresh_token');
      params.append('scope', currentScope);

      console.log(`[MsalService] Request URL: ${this.TOKEN_URL}`);
      console.log(`[MsalService] Request params:`, {
        client_id: clientId,
        grant_type: 'refresh_token',
        scope: currentScope,
        refresh_token: `[HIDDEN - Length: ${refreshToken.length}]`
      });

      // Log the actual serialized body for debugging
      const serializedBody = params.toString();
      console.log(`[MsalService] Serialized body length: ${serializedBody.length}`);
      console.log(`[MsalService] Serialized body (partial):`, serializedBody.replace(/refresh_token=[^&]+/, 'refresh_token=[HIDDEN]'));

      const config = {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'application/json',
          'Accept-Encoding': 'gzip, deflate, br'
        },
        timeout: 30000, // 30 seconds timeout
      };

      try {
        // Use URLSearchParams with fixed space encoding
        const bodyString = params.toString().replace(/\+/g, '%20');

        logger.info(`[MsalService] Attempting token request with scope: ${currentScope}`);
        const response = await axios.post(this.TOKEN_URL, bodyString, {
          headers: config.headers,
          timeout: config.timeout
        });

        logger.info({ status: response.status, scope: currentScope }, `[MsalService] Token request successful with scope variant ${scopeIndex + 1}`);
        return this.validateTokenResponse(response.data);

      } catch (error) {
        lastError = this.handleTokenError(error, `scope variant ${scopeIndex + 1}: ${currentScope}`);
        logger.warn(`[MsalService] Scope variant ${scopeIndex + 1} failed: ${lastError.message}`);

        // If this is not the last scope variant, continue to next one
        if (scopeIndex < scopesToTry.length - 1) {
          logger.info(`[MsalService] Trying next scope variant...`);
          continue;
        }
      }
    }

    // If all scope variants failed, throw the last error
    throw lastError || new Error('All scope variants failed');
  }

  /**
   * Validates and normalizes token response
   */
  private static validateTokenResponse(data: any): TokenResponse {
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid token response format');
    }

    if (!data.access_token) {
      throw new Error('Access token not found in response');
    }

    return {
      access_token: data.access_token,
      refresh_token: data.refresh_token,
      expires_in: data.expires_in || 3600,
      scope: data.scope || 'default',
      token_type: data.token_type || 'Bearer',
    };
  }

  /**
   * Handles and categorizes token acquisition errors
   */
  private static handleTokenError(error: unknown, context: string): Error {
    const logger = getLogger();

    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError<MsalError>;

      // Log detailed error information for debugging
      logger.error({
        context,
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        headers: axiosError.response?.headers,
        data: axiosError.response?.data,
        code: axiosError.code,
        message: axiosError.message
      }, `[MsalService] Detailed error information for ${context}`);

      if (axiosError.response?.data) {
        const msalError = axiosError.response.data;
        const errorMsg = `Microsoft OAuth2 error (${context}): ${msalError.error} - ${msalError.error_description}`;

        // Log Microsoft-specific error details
        logger.error({
          error: msalError.error,
          error_description: msalError.error_description,
          error_codes: msalError.error_codes,
          timestamp: msalError.timestamp,
          trace_id: msalError.trace_id,
          correlation_id: msalError.correlation_id
        }, `[MsalService] Microsoft OAuth2 error details`);

        // Categorize specific errors
        if (msalError.error === 'invalid_grant') {
          return new Error(`${errorMsg}. The refresh token may be expired or revoked. Please re-authenticate.`);
        } else if (msalError.error === 'invalid_client') {
          return new Error(`${errorMsg}. The client ID may be invalid or unauthorized.`);
        } else if (msalError.error === 'unauthorized_client') {
          return new Error(`${errorMsg}. The client is not authorized for this operation.`);
        }

        return new Error(errorMsg);
      } else if (axiosError.code === 'ECONNABORTED') {
        return new Error(`Token request timeout (${context}). Please check your internet connection.`);
      } else if (axiosError.code === 'ENOTFOUND' || axiosError.code === 'ECONNREFUSED') {
        return new Error(`Network error (${context}): ${axiosError.message}`);
      }

      return new Error(`HTTP error (${context}): ${axiosError.message}`);
    }

    logger.error({ context, error }, `[MsalService] Non-axios error occurred`);
    return new Error(`Unknown error (${context}): ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  /**
   * Simple delay utility for retry logic
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Generates OAuth2 authentication string for IMAP XOAUTH2
   */
  static generateAuthString(email: string, accessToken: string): string {
    return `user=${email}\x01auth=Bearer ${accessToken}\x01\x01`;
  }

  /**
   * Checks if an error indicates the need for re-authentication
   */
  static isReauthenticationRequired(error: Error): boolean {
    const message = error.message.toLowerCase();
    return message.includes('invalid_grant') ||
           message.includes('expired') ||
           message.includes('revoked') ||
           message.includes('unauthorized');
  }
}