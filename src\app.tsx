import React, { useEffect } from 'react';
import { Toaster } from 'sonner';

import ErrorBoundary from './components/ErrorBoundary';
import Layout from './components/Layout';
import { useAccountInitializer } from './shared/hooks/useAccountInitializer';
import { useAccountStore } from './shared/store/accounts/accountStore';
import { useLogStore } from './shared/store/logStore';
import { ThemeProvider } from './shared/ui/theme-provider';
import { logger } from './shared/lib/logger';


const App = (): React.JSX.Element => {
  const { addLog } = useLogStore();
  const { setAccountConnectionStatus } = useAccountStore();

  logger.info('App component rendering...');

  // Initialize the application (load accounts, proxy settings, etc.)
  useAccountInitializer();

  useEffect(() => {
    try {
      // Listen for log messages from the main process
      window.ipcApi.on('log:add', (log: any) => {
        addLog(log);
      });

      // Listen for account connection status updates
      window.ipcApi.on('account:connection-status', ({ accountId, status }: { accountId: string, status: 'connected' | 'connecting' | 'disconnected' }) => {
        setAccountConnectionStatus(accountId, status);
      });

      // Notify the main process that the renderer is ready
      void window.ipcApi.rendererReady();
    } catch (error) {
      logger.error(`Failed to initialize application: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [addLog, setAccountConnectionStatus]);

  return (
    <ThemeProvider defaultTheme="dark" storageKey="imapviewer-ui-theme">
      <ErrorBoundary>
        <Layout />
        <Toaster
          position="top-right"
          richColors
          closeButton
          toastOptions={{
            style: {
              background: 'hsl(var(--background))',
              border: '1px solid hsl(var(--border))',
              color: 'hsl(var(--foreground))',
            },
          }}
        />
      </ErrorBoundary>
    </ThemeProvider>
  );
};

export default App;