/**
 * @file Account section component for the top bar
 */
import { <PERSON><PERSON>s, ChevronLeft, ChevronRight, RefreshCw, Users, FolderOpen } from 'lucide-react';
import React from 'react';

import { cn } from '../utils/utils';

import { Button } from './button';

interface TopBarAccountSectionProps {
  accountCount: number;
  isCollapsed: boolean;
  isConnectingAll: boolean;
  isSettingsOpen?: boolean;
  onSettings: () => void;
  onToggleCollapse: () => void;
  onConnectAll: () => void;
  onDataFiles?: () => void;
  dataFilesButtonRef?: React.RefObject<HTMLButtonElement>;
}

/**
 * Account section for the top bar showing account count and actions
 */
export const TopBarAccountSection: React.FC<TopBarAccountSectionProps> = ({
  accountCount,
  isCollapsed,
  isConnectingAll,
  isSettingsOpen = false,
  onSettings,
  onToggleCollapse,
  onConnectAll,
  onDataFiles,
  dataFilesButtonRef
}) => {
  return (
    <div className="flex items-center gap-2">
      {/* Connect All + Account Status */}
      <div className="flex items-center gap-1">
        {/* Data Files Button */}
        {onDataFiles && (
          <Button
            ref={dataFilesButtonRef}
            variant="ghost"
            size="icon"
            onClick={onDataFiles}
            className="h-8 w-8"
            title="Access data files"
          >
            <FolderOpen size={16} />
          </Button>
        )}

        {/* Connect All Action - только иконка */}
        {accountCount > 0 && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onConnectAll}
            disabled={isConnectingAll}
            className={cn("h-8 w-8", isConnectingAll && "animate-pulse")}
            title={isConnectingAll ? "Connecting to all accounts..." : "Connect to all accounts"}
          >
            <RefreshCw size={16} className={isConnectingAll ? "animate-spin" : ""} />
          </Button>
        )}

        {/* Account Count */}
        <div className="flex items-center gap-1.5 text-sm text-muted-foreground">
          <Users size={20} className="text-primary" />
          <span className="font-medium">{accountCount}</span>
          <span>{accountCount === 1 ? 'Account' : 'Accounts'}</span>
        </div>
      </div>

      {/* Settings Button */}
      <Button
        variant={isSettingsOpen ? "default" : "ghost"}
        size="icon"
        onClick={onSettings}
        className="h-8 w-8"
        title="Settings"
      >
        <Settings size={16} />
      </Button>

      {/* Collapse/Expand Button */}
      <Button
        variant="ghost"
        size="icon"
        onClick={onToggleCollapse}
        className="h-8 w-8"
        title={isCollapsed ? "Expand account panel" : "Collapse account panel"}
      >
        {isCollapsed ? <ChevronLeft size={16} /> : <ChevronRight size={16} />}
      </Button>
    </div>
  );
};
