/**
 * @file Centralized parser for account strings.
 * Provides robust logic for parsing and serializing account data,
 * especially for complex Microsoft OAuth formats.
 */

import { v5 as uuidv5 } from 'uuid';
import type { Account } from '../../shared/types/account';

// A constant namespace for generating deterministic UUIDs from email addresses.
const ACCOUNT_ID_NAMESPACE = 'fd5a1e70-03e3-4d40-b8b0-3f7b9d10c0f2';

export interface ParsedAccountData {
  email: string;
  password?: string;
  refreshToken?: string;
  clientId?: string;
}

/**
 * Parses a single account string into a structured object.
 * This function is designed to be robust against separators appearing within tokens.
 * Format: email:password:refreshToken:clientId or email|password|refreshToken|clientId
 * The password field is optional for token-based auth.
 * @param line The string to parse.
 * @returns A ParsedAccountData object or null if parsing fails.
 */
export function parseAccountString(line: string): ParsedAccountData | null {
  if (!line || !line.trim()) {
    return null;
  }

  const cleanLine = line.trim();
  const separator = cleanLine.includes('|') ? '|' : ':';
  const parts = cleanLine.split(separator);

  if (parts.length < 2) {
    return null; // Must have at least email and password/token
  }

  const email = parts[0];
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return null; // First part must be a valid email
  }

  // Handle simple email:password format
  if (parts.length === 2) {
    return { email, password: parts[1] };
  }
  
  // Handle complex Microsoft format: email:password:refreshToken:clientId
  // 1. Email is the first part.
  // 2. Password is the second part.
  // 3. ClientID is the *last* part (assuming it's a UUID).
  // 4. RefreshToken is everything in between.
  const clientIdCandidate = parts[parts.length - 1];
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

  if (parts.length >= 4 && uuidRegex.test(clientIdCandidate)) {
    const password = parts[1];
    const refreshToken = parts.slice(2, -1).join(separator);
    return {
      email,
      password,
      refreshToken,
      clientId: clientIdCandidate,
    };
  }

  // Fallback for formats like email:password:token (where token is not separated from password)
  const password = parts[1];
  const rest = parts.slice(2).join(separator);
  // If 'rest' looks like a Microsoft token, we assume it's part of the password field
  if (rest.length > 50 && (rest.includes('M.C') || rest.includes('EwA'))) {
      return { email, password: `${password}:${rest}` };
  }

  // Default to email:password if no other format matches
  return { email, password: parts.slice(1).join(separator) };
}

/**
 * Serializes an Account object into a string for file storage.
 * @param account The account object to serialize.
 * @returns A string in the format email:password:refreshToken:clientId.
 */
export function serializeAccountToString(account: Partial<Account>): string {
  const parts = [account.email, account.password];

  if (account.refreshToken) {
    parts.push(account.refreshToken);
  }
  
  if (account.clientId) {
    // Ensure clientId is always the last part if refreshToken is also present
    if (!account.refreshToken) {
        // Add an empty placeholder for refreshToken if it's missing but clientId is present
        parts.push(''); 
    }
    parts.push(account.clientId);
  }

  return parts.join(':');
}

/**
 * Creates a full Account object from parsed data.
 * @param parsed The parsed account data.
 * @returns A full Account object.
 */
export function createAccountFromParsedData(parsed: ParsedAccountData): Omit<Account, 'id' | 'connectionStatus'> {
    return {
        displayName: parsed.email.split('@')[0],
        email: parsed.email,
        password: parsed.password ?? '',
        refreshToken: parsed.refreshToken,
        clientId: parsed.clientId,
        incoming: { protocol: 'imap', host: 'outlook.office365.com', port: 993, useTls: true },
        useProxy: false,
    };
}