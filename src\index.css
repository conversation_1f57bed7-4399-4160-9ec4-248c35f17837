@import "tailwindcss";

:root {
  /* ShadCN UI Design System Colors - Dark Purple Theme */
  --background: 260 6% 8%;
  --foreground: 0 0% 98%;
  --card: 260 6% 10%;
  --card-foreground: 0 0% 98%;
  --popover: 260 6% 10%;
  --popover-foreground: 0 0% 98%;
  --primary: 263 70% 50%;
  --primary-foreground: 0 0% 98%;
  --secondary: 260 6% 16%;
  --secondary-foreground: 0 0% 98%;
  --muted: 260 6% 16%;
  --muted-foreground: 0 0% 63.9%;
  --accent: 263 70% 50%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 50%;
  --destructive-foreground: 0 0% 98%;
  --border: 260 6% 20%;
  --input: 260 6% 16%;
  --ring: 263 70% 50%;
  --chart-1: 263 70% 50%;
  --chart-2: 280 65% 60%;
  --chart-3: 240 65% 60%;
  --chart-4: 300 65% 60%;
  --chart-5: 320 65% 60%;

  /* Custom theme extensions */
  --radius: 0.75rem;

  /* Purple theme colors */
  --purple-primary: 263 70% 50%;
  --purple-secondary: 280 65% 60%;
  --purple-accent: 240 65% 60%;
  --success: 142 76% 36%;
  --warning: 38 92% 50%;
  --info: 199 89% 48%;
}

/* Add the ShadCN CSS variables to our theme layer */
@theme {
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));

  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
}

/* Light theme overrides */
.light {
  --background: 0 0% 100%;
  --foreground: 260 6% 8%;
  --card: 0 0% 100%;
  --card-foreground: 260 6% 8%;
  --popover: 0 0% 100%;
  --popover-foreground: 260 6% 8%;
  --primary: 263 70% 50%;
  --primary-foreground: 0 0% 98%;
  --secondary: 260 6% 96%;
  --secondary-foreground: 260 6% 8%;
  --muted: 260 6% 96%;
  --muted-foreground: 260 6% 45%;
  --accent: 263 70% 50%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 260 6% 89%;
  --input: 260 6% 89%;
  --ring: 263 70% 50%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Utility classes for common layouts */
  .flex-center {
    @apply flex items-center justify-center;
  }

  .flex-between {
    @apply flex items-center justify-between;
  }

  .stack {
    @apply flex flex-col;
  }


}

:root {
  /* Application-specific variables */
  --app-accent-blue: #3ea6ff;
  --app-accent-red: #ff4e45;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Animation classes - optimized for performance */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
  will-change: opacity;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
  will-change: transform, opacity;
}

.pulse {
  animation: pulse 2s infinite;
  will-change: opacity;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translate3d(0, 10px, 0);
    opacity: 0;
  }
  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Base styles */
html, body, #root {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

/* Modern scrollbar for webkit browsers - optimized */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  transition: background-color 0.3s ease;
  will-change: background-color;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Hide horizontal scrollbar in some components */
.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Hide scrollbar for Chrome, Safari and Opera */
}

.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Placeholder shimmer effect */
@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
  background: linear-gradient(to right, rgba(255,255,255,0.03) 8%, rgba(255,255,255,0.08) 18%, rgba(255,255,255,0.03) 33%);
  background-size: 1000px 100%;
  will-change: background-position;
}

/* Enhanced focus styles for better accessibility */
*:focus-visible {
  outline: 2px solid #60a5fa;
  outline-offset: 2px;
  border-radius: 4px;
}

/* High contrast focus for interactive elements */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
[role="button"]:focus-visible,
[role="tab"]:focus-visible,
[role="menuitem"]:focus-visible {
  outline: 2px solid #60a5fa;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(96, 165, 250, 0.2);
}

/* Remove focus outline for mouse users but keep for keyboard */
.js-focus-visible :focus:not(.focus-visible) {
  outline: none;
}

/* Skip to main content link for screen readers */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-background: 0 0% 0%;
    --color-foreground: 0 0% 100%;
    --color-border: 0 0% 30%;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
