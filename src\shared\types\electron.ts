/**
 * @file Electron type definitions for IPC communication
 */

import type { Buffer } from 'buffer';

import type { Account, ProxyConfig, GlobalProxyConfig } from './account';
import type { EmailHeader } from './email';

export type ProxyStatus = 'disabled' | 'enabled' | 'connecting' | 'connected' | 'error';

export interface MailBoxes {
  [key: string]: {
    attribs?: string[];
    children?: MailBoxes;
    delimiter: string;
  };
}

export interface IIpcAPI {
  // Account management
  discoverEmailConfig: (_domain: string, _force?: boolean) => Promise<unknown>;
  getAccounts: () => Promise<Account[]>;
  addAccount: (_accountData: Omit<Account, 'id'>) => Promise<Account>;
  updateAccount: (_accountId: string, _accountData: Partial<Omit<Account, 'id'>>) => Promise<Account>;
  deleteAccount: (_accountId: string) => Promise<{ success: boolean }>;

  // Event listeners
  on: <T = unknown>(_event: string, _callback: (_data: T) => void) => void;
  rendererReady: () => void;

  // Import functionality
  importFromFileContent: (_content: string) => Promise<{ addedCount: number; skippedCount: number; error?: string }>;
  importFromFileInstant: (_content: string) => Promise<{ addedCount: number; skippedCount: number; error?: string }>;

  // Inbox watching
  watchInbox: (_accountId: string) => Promise<void>;

  // Mailbox operations
  getMailboxes: (_accountId: string) => Promise<MailBoxes>;
  selectMailbox: (_accountId: string, _mailboxName: string, _pageSize: number) => Promise<unknown>;
  initializeAccount: (_accountId: string, _initialEmailLimit?: number) => Promise<{
    mailboxes: MailBoxes;
    defaultMailbox: string;
    initialEmails: EmailHeader[];
    totalEmailCount: number;
  }>;

  // Email operations
  getEmails: (_accountId: string, _mailboxName: string, _offset: number, _limit: number) => Promise<EmailHeader[]>;
  getEmailBody: (_accountId: string, _mailboxName: string, _uid: number) => Promise<unknown>;
  downloadAttachment: (_accountId: string, _mailboxName: string, _emailUid: number, _attachmentIndex: number) => Promise<{ filename: string; contentType: string; content: Buffer; size: number }>;
  deleteEmail: (_accountId: string, _mailboxName: string, _uid: number) => Promise<void>;
  deleteEmails: (_accountId: string, _mailboxName: string, _uids: number[]) => Promise<void>;
  markAsSeen: (_accountId: string, _emailUid: number, _mailbox: string) => Promise<void>;
  markAsUnseen: (_accountId: string, _emailUid: number, _mailbox: string) => Promise<void>;

  // Event handlers
  onNewMail: (_callback: (_event: unknown, _data: { accountId: string; mailboxName: string; newMailCount: number }) => void) => () => void;
  onLog: (callback: (_event: unknown, _log: { level: 'info' | 'success' | 'error'; message: string }) => void) => (() => void);
  logMessage: (level: 'info' | 'warn' | 'error', message: string, context?: object) => void;


  // Proxy management
  proxy: {
    getGlobal: () => Promise<GlobalProxyConfig | null>;
    setGlobal: (_config: GlobalProxyConfig | null) => void;
    onStatusUpdate: (_callback: (_event: unknown, _status: { status: ProxyStatus; ip?: string; error?: string }) => void) => void;
  };

  // Proxy list management
  getProxyList: () => Promise<ProxyConfig[]>;
  saveProxyList: (_proxies: ProxyConfig[]) => Promise<void>;
  testProxy: (_config: ProxyConfig) => Promise<{ success: boolean; ip?: string; error?: string }>;

  // User config management
  getUserConfig: () => Promise<Record<string, unknown> | null>;
  saveUserConfig: (_config: Record<string, unknown>) => Promise<void>;

  // File operations
  openDataFolder: () => Promise<{ success: boolean; error?: string }>;
  openAccountsFile: () => Promise<{ success: boolean; error?: string }>;
  openConfigFile: () => Promise<{ success: boolean; error?: string }>;
  getDataDir: () => Promise<string>;

  // Clipboard operations
  detectCredentialsFromClipboard: () => Promise<unknown>;
  parseCredentialsString: (_text: string) => Promise<unknown>;
  copyAccountCredentials: (_email: string, _password: string) => Promise<unknown>;

  // IMAP operations (legacy)
  imap: {
    getMailboxes: (_accountId: string) => Promise<unknown>;
    getEmails: (_accountId: string, _mailboxName: string, _offset: number, _limit: number) => Promise<EmailHeader[]>;
    getEmailBody: (_accountId: string, _emailUid: number, _mailbox: string) => Promise<unknown>;
    deleteEmail: (_accountId: string, _emailUid: number, _mailbox: string) => Promise<void>;
    markAsSeen: (_accountId: string, _emailUid: number, _mailbox: string) => Promise<void>;
    markAsUnseen: (_accountId: string, _emailUid: number, _mailbox: string) => Promise<void>;
    deleteEmails: (_accountId: string, _emailUids: number[], _mailbox: string) => Promise<void>;
  };
}
