/**
 * @file Simple hook for managing application initialization
 */
import { useState, useEffect, useCallback } from 'react';

import { useAccountStore } from '../store/accounts/accountStore';
import { useMainSettingsStore } from '../store/mainSettingsStore';
import { useProxyStore } from '../store/proxyStore';
import type { MailBoxes } from '../types/electron';

interface UseAccountInitializerReturn {
  isInitialized: boolean;
  isInitializing: boolean;
  initializationError: string | null;
  mailboxes: MailBoxes | null;
  isLoadingAccounts: boolean;
  retryInitialization: () => Promise<void>;
  initializeAccount: (accountId: string, forceRefresh?: boolean) => Promise<void>;
}

/**
 * Simple hook for managing account initialization with coordination
 */
export const useAccountInitializer = (): UseAccountInitializerReturn => {
  const {
    setAccounts,
    accounts,
    selectedAccountId,
    selectMailbox,
    setMailboxesForAccount,
    isAccountSwitching,
    finishAccountSwitch
  } = useAccountStore();
  const { initializeProxy } = useProxyStore();
  const { settings } = useMainSettingsStore();

  const [isInitialized, setIsInitialized] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [initializationError, setInitializationError] = useState<string | null>(null);
  const [isLoadingAccounts, setIsLoadingAccounts] = useState(false);
  const [mailboxes, setMailboxes] = useState<MailBoxes | null>(null);

  const loadAccounts = useCallback(async () => {
    setIsLoadingAccounts(true);
    try {
      const accountsData = await window.ipcApi.getAccounts();
      setAccounts(accountsData);
    } catch (error) {
      console.error('Failed to load accounts:', error);
      throw error;
    } finally {
      setIsLoadingAccounts(false);
    }
  }, [setAccounts]);

  const initializeAccount = useCallback(async (accountId: string, forceRefresh = false) => {
    if (!accountId) return;

    try {
      // Load mailboxes for the account
      const accountMailboxes = await window.ipcApi.getMailboxes(accountId);
      setMailboxesForAccount(accountId, accountMailboxes);
      
      if (selectedAccountId === accountId) {
        setMailboxes(accountMailboxes);
        
        // Auto-select INBOX if available
        if (accountMailboxes && accountMailboxes['INBOX']) {
          selectMailbox('INBOX');
        } else if (accountMailboxes) {
          const firstMailbox = Object.keys(accountMailboxes)[0];
          if (firstMailbox) {
            selectMailbox(firstMailbox);
          }
        }
        
        // Mark account switch as finished
        finishAccountSwitch();
      }
    } catch (error) {
      console.error(`Failed to initialize account ${accountId}:`, error);
      finishAccountSwitch(); // Always finish switch even on error
    }
  }, [selectedAccountId, setMailboxesForAccount, setMailboxes, selectMailbox, finishAccountSwitch]);

  const retryInitialization = useCallback(async () => {
    setInitializationError(null);
    setIsInitializing(true);
    
    try {
      await loadAccounts();
      await initializeProxy();
      setIsInitialized(true);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setInitializationError(errorMessage);
    } finally {
      setIsInitializing(false);
    }
  }, [loadAccounts, initializeProxy]);

  // Initialize on mount
  useEffect(() => {
    if (!isInitialized && !isInitializing) {
      retryInitialization();
    }
  }, [isInitialized, isInitializing, retryInitialization]);

  // Initialize account when selection changes, but only if not currently switching
  useEffect(() => {
    if (isInitialized && selectedAccountId && isAccountSwitching) {
      initializeAccount(selectedAccountId);
    }
  }, [isInitialized, selectedAccountId, isAccountSwitching, initializeAccount]);

  return {
    isInitialized,
    isInitializing,
    initializationError,
    mailboxes,
    isLoadingAccounts,
    retryInitialization,
    initializeAccount,
  };
};
